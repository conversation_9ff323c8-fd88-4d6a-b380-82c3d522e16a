#!/usr/bin/env python3
"""
平衡性能测试脚本
测试修复后的 nonplanar_slicer 060502.py 在不同模型上的稳定性
"""

import time
import os
import sys
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入修复后的切片器
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("nonplanar_slicer", "nonplanar_slicer 060502.py")
    nonplanar_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(nonplanar_module)
    DirectProjectionSlicer = nonplanar_module.DirectProjectionSlicer
    print("✅ 成功导入修复后的切片器")
except ImportError as e:
    print(f"❌ 导入切片器失败: {e}")
    sys.exit(1)

def test_single_model(model_path):
    """测试单个模型"""
    print(f"\n🔍 测试模型: {os.path.basename(model_path)}")
    print("="*50)
    
    # 平衡的测试配置
    test_config = {
        'target_bead_width': 0.6,
        'path_row_spacing': 0.6 * 0.7,
        'path_max_segment_length': 0.6 * 2,
        'offset_distance_param': 0.6 * 0.1,
        'proximity_threshold_param': 0.1,
        # 平衡的迭代参数
        'iter_min_delta_y_factor_param': 0.015,
        'iter_max_delta_y_factor_param': 2.0,
        'iter_tolerance_abs_param': 0.008,
        'iter_max_iterations_per_step_param': 8,
        'iter_num_samples_for_spacing_calc_param': 6
    }
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 创建切片器实例
        print("🔧 初始化切片器...")
        init_start = time.time()
        projector = DirectProjectionSlicer(
            mesh_path=model_path,
            target_surface_distance=test_config['target_bead_width'],
            slice_direction='x',
            inward_normals=True,
            min_points_req=2
        )
        init_time = time.time() - init_start
        print(f"   初始化耗时: {init_time:.3f} 秒")
        
        # 生成路径
        print("🛠️  开始路径生成...")
        path_start = time.time()
        paths_result = projector.create_projected_fill_paths(
            row_spacing=test_config['path_row_spacing'],
            offset_distance=test_config['offset_distance_param'],
            max_segment_length=test_config['path_max_segment_length'],
            strategy='direct_offset',
            proximity_threshold=test_config['proximity_threshold_param'],
            adaptive_density=True,
            iter_min_delta_y_factor=test_config['iter_min_delta_y_factor_param'],
            iter_max_delta_y_factor=test_config['iter_max_delta_y_factor_param'],
            iter_tolerance_abs=test_config['iter_tolerance_abs_param'],
            iter_max_iterations_per_step=test_config['iter_max_iterations_per_step_param'],
            iter_num_samples_for_spacing_calc=test_config['iter_num_samples_for_spacing_calc_param']
        )
        path_time = time.time() - path_start
        
        # 总耗时
        total_time = time.time() - start_time
        
        # 获取性能报告
        perf_report = projector.get_performance_report()
        
        # 分析结果
        if paths_result and len(paths_result) >= 2:
            paths_list, spacing_data = paths_result
            
            # 计算路径点总数
            total_points = sum(len(points) for points, _, _, _ in paths_list if points is not None)
            
            print(f"\n📊 测试结果:")
            print(f"   ✅ 成功 - 总耗时: {total_time:.2f}s")
            print(f"   - 初始化时间: {init_time:.3f}s")
            print(f"   - 路径生成时间: {path_time:.2f}s")
            print(f"   - 缓存命中率: {perf_report['cache_hit_rate']:.1f}%")
            print(f"   - 总计算次数: {perf_report['total_calculations']}")
            print(f"   - 收敛次数: {perf_report['convergence_count']}")
            print(f"   - 间距警告: {perf_report['spacing_warnings']} 次")
            print(f"   - 路径段数量: {len(paths_list)}")
            print(f"   - 总路径点数: {total_points}")
            
            # 质量分析
            if spacing_data:
                try:
                    spacings = [data['measured_3d_distance'] for data in spacing_data if data.get('measured_3d_distance', 0) > 0]
                    
                    if spacings:
                        spacings = np.array(spacings)
                        target_spacing = test_config['path_row_spacing']
                        
                        # 计算基本统计
                        mean_spacing = np.mean(spacings)
                        std_spacing = np.std(spacings)
                        
                        # 计算目标达成率
                        tolerance_5 = target_spacing * 0.05
                        within_tolerance = np.abs(spacings - target_spacing) <= tolerance_5
                        target_achievement_rate = np.mean(within_tolerance) * 100
                        
                        # 计算RMS误差
                        rms_error = np.sqrt(np.mean((spacings - target_spacing) ** 2))
                        rms_error_percent = (rms_error / target_spacing) * 100
                        
                        print(f"\n🎯 质量指标:")
                        print(f"   - 平均间距: {mean_spacing:.3f} mm (目标: {target_spacing:.3f} mm)")
                        print(f"   - 标准差: {std_spacing:.3f} mm")
                        print(f"   - 目标达成率 (±5%): {target_achievement_rate:.1f}%")
                        print(f"   - RMS误差: {rms_error_percent:.1f}%")
                        
                        # 评估结果
                        issues = []
                        if target_achievement_rate < 99.0:
                            issues.append(f"目标达成率过低: {target_achievement_rate:.1f}% < 99%")
                        if rms_error_percent > 1.7:
                            issues.append(f"RMS误差过高: {rms_error_percent:.1f}% > 1.7%")
                        if perf_report['spacing_warnings'] > 0:
                            issues.append(f"间距警告: {perf_report['spacing_warnings']} 次 > 0")
                        if perf_report['cache_hit_rate'] < 5.0:
                            issues.append(f"缓存命中率过低: {perf_report['cache_hit_rate']:.1f}% < 5%")
                        if total_time > 10.0:
                            issues.append(f"执行时间过长: {total_time:.1f}s > 10s")
                        
                        if issues:
                            print(f"\n⚠️  发现问题:")
                            for issue in issues:
                                print(f"   - {issue}")
                        else:
                            print(f"\n✅ 所有质量指标达标!")
                            
                        return {
                            'success': True,
                            'total_time': total_time,
                            'cache_hit_rate': perf_report['cache_hit_rate'],
                            'target_achievement_rate': target_achievement_rate,
                            'rms_error_percent': rms_error_percent,
                            'spacing_warnings': perf_report['spacing_warnings'],
                            'issues': issues
                        }
                    else:
                        print(f"\n⚠️  无有效间距数据")
                        return {'success': False, 'error': '无有效间距数据'}
                        
                except Exception as e:
                    print(f"\n❌ 质量分析失败: {e}")
                    return {'success': False, 'error': f'质量分析失败: {e}'}
            else:
                print(f"\n⚠️  无间距数据")
                return {'success': False, 'error': '无间距数据'}
        else:
            print(f"\n❌ 路径生成失败或结果为空")
            return {'success': False, 'error': '路径生成失败'}
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🔬 平衡性能测试 - nonplanar_slicer 修复版本")
    print("="*60)
    
    # 测试几个代表性模型
    test_models = [
        "src/mengpi_mnp_000.stl",  # 之前成功的模型
        "src/m21_mnp_001.stl",     # 中等复杂度模型
        "src/xiekua_mnp_001.stl",  # 另一个测试模型
    ]
    
    results = []
    for model in test_models:
        if os.path.exists(model):
            result = test_single_model(model)
            result['model'] = os.path.basename(model)
            results.append(result)
        else:
            print(f"\n⚠️  模型文件不存在: {model}")
    
    # 生成总结报告
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    successful_tests = [r for r in results if r.get('success', False)]
    failed_tests = [r for r in results if not r.get('success', False)]
    
    print(f"📈 总体结果:")
    print(f"   - 测试模型数: {len(results)}")
    print(f"   - 成功: {len(successful_tests)} ✅")
    print(f"   - 失败: {len(failed_tests)} ❌")
    print(f"   - 成功率: {len(successful_tests)/len(results)*100:.1f}%")
    
    if successful_tests:
        print(f"\n⏱️  性能统计:")
        times = [r['total_time'] for r in successful_tests]
        cache_rates = [r['cache_hit_rate'] for r in successful_tests]
        achievement_rates = [r['target_achievement_rate'] for r in successful_tests if 'target_achievement_rate' in r]
        rms_errors = [r['rms_error_percent'] for r in successful_tests if 'rms_error_percent' in r]
        
        print(f"   - 平均执行时间: {np.mean(times):.2f}s")
        print(f"   - 平均缓存命中率: {np.mean(cache_rates):.1f}%")
        if achievement_rates:
            print(f"   - 平均目标达成率: {np.mean(achievement_rates):.1f}%")
        if rms_errors:
            print(f"   - 平均RMS误差: {np.mean(rms_errors):.1f}%")
    
    print(f"\n📋 详细结果:")
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['model']}")
        if result.get('success'):
            print(f"   ✅ 成功 - {result['total_time']:.2f}s")
            if result.get('issues'):
                print(f"   ⚠️  问题: {len(result['issues'])} 个")
        else:
            print(f"   ❌ 失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
