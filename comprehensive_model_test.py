#!/usr/bin/env python3
"""
全面模型测试脚本
用于测试 nonplanar_slicer 060502.py 在不同STL模型上的稳定性和性能
"""

import time
import os
import sys
import numpy as np
from pathlib import Path
import glob

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入优化后的切片器
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("nonplanar_slicer", "nonplanar_slicer 060502.py")
    nonplanar_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(nonplanar_module)
    DirectProjectionSlicer = nonplanar_module.DirectProjectionSlicer
    print("✅ 成功导入优化后的切片器")
except ImportError as e:
    print(f"❌ 导入切片器失败: {e}")
    sys.exit(1)

def find_test_models():
    """查找可用的测试模型"""
    test_patterns = [
        "src/*.stl",
        "*.stl",
        "models/*.stl",
        "test_models/*.stl"
    ]
    
    models = []
    for pattern in test_patterns:
        models.extend(glob.glob(pattern))
    
    # 去重并排序
    models = sorted(list(set(models)))
    return models

def analyze_model_complexity(mesh_path):
    """分析模型复杂度"""
    try:
        import trimesh
        mesh = trimesh.load(mesh_path)
        
        complexity = {
            'vertices': len(mesh.vertices),
            'faces': len(mesh.faces),
            'volume': mesh.volume if hasattr(mesh, 'volume') else 0,
            'surface_area': mesh.area if hasattr(mesh, 'area') else 0,
            'bounds_size': np.linalg.norm(mesh.bounds[1] - mesh.bounds[0]) if hasattr(mesh, 'bounds') else 0
        }
        
        # 计算复杂度等级
        total_elements = complexity['vertices'] + complexity['faces']
        if total_elements < 1000:
            complexity['level'] = 'Simple'
        elif total_elements < 10000:
            complexity['level'] = 'Medium'
        elif total_elements < 50000:
            complexity['level'] = 'Complex'
        else:
            complexity['level'] = 'Very Complex'
            
        return complexity
    except Exception as e:
        return {'error': str(e), 'level': 'Unknown'}

def test_model_performance(model_path, test_config):
    """测试单个模型的性能"""
    print(f"\n🔍 测试模型: {model_path}")
    print("="*60)
    
    # 分析模型复杂度
    complexity = analyze_model_complexity(model_path)
    print(f"📊 模型复杂度: {complexity.get('level', 'Unknown')}")
    if 'vertices' in complexity:
        print(f"   - 顶点数: {complexity['vertices']:,}")
        print(f"   - 面数: {complexity['faces']:,}")
        print(f"   - 总元素: {complexity['vertices'] + complexity['faces']:,}")
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 创建切片器实例
        print("🔧 初始化切片器...")
        init_start = time.time()
        projector = DirectProjectionSlicer(
            mesh_path=model_path,
            target_surface_distance=test_config['target_bead_width'],
            slice_direction='x',
            inward_normals=True,
            min_points_req=2
        )
        init_time = time.time() - init_start
        print(f"   初始化耗时: {init_time:.3f} 秒")
        
        # 生成路径
        print("🛠️  开始路径生成...")
        path_start = time.time()
        paths_result = projector.create_projected_fill_paths(
            row_spacing=test_config['path_row_spacing'],
            offset_distance=test_config['offset_distance_param'],
            max_segment_length=test_config['path_max_segment_length'],
            strategy='direct_offset',
            proximity_threshold=test_config['proximity_threshold_param'],
            adaptive_density=True,
            iter_min_delta_y_factor=test_config['iter_min_delta_y_factor_param'],
            iter_max_delta_y_factor=test_config['iter_max_delta_y_factor_param'],
            iter_tolerance_abs=test_config['iter_tolerance_abs_param'],
            iter_max_iterations_per_step=test_config['iter_max_iterations_per_step_param'],
            iter_num_samples_for_spacing_calc=test_config['iter_num_samples_for_spacing_calc_param']
        )
        path_time = time.time() - path_start
        
        # 总耗时
        total_time = time.time() - start_time
        
        # 获取性能报告
        perf_report = projector.get_performance_report()
        
        # 分析结果
        result = {
            'model_path': model_path,
            'complexity': complexity,
            'success': False,
            'total_time': total_time,
            'init_time': init_time,
            'path_time': path_time,
            'performance': perf_report,
            'quality_metrics': {},
            'issues': []
        }
        
        if paths_result and len(paths_result) >= 2:
            paths_list, spacing_data = paths_result
            
            # 计算路径点总数
            total_points = sum(len(points) for points, _, _, _ in paths_list if points is not None)
            
            result.update({
                'success': True,
                'path_count': len(paths_list),
                'spacing_data_count': len(spacing_data) if spacing_data else 0,
                'total_points': total_points
            })
            
            # 质量分析
            if spacing_data:
                try:
                    quality_metrics = analyze_quality_metrics(spacing_data, test_config['path_row_spacing'])
                    result['quality_metrics'] = quality_metrics

                    # 检查质量问题
                    if quality_metrics.get('target_achievement_rate', 0) < 99.0:
                        result['issues'].append(f"目标达成率过低: {quality_metrics.get('target_achievement_rate', 0):.1f}% < 99%")

                    if quality_metrics.get('rms_error_percent', 100) > 1.7:
                        result['issues'].append(f"RMS误差过高: {quality_metrics.get('rms_error_percent', 100):.1f}% > 1.7%")
                except Exception as e:
                    result['issues'].append(f"质量分析失败: {str(e)}")

                if perf_report['spacing_warnings'] > 0:
                    result['issues'].append(f"间距警告: {perf_report['spacing_warnings']} 次 > 0")
            
            # 检查性能问题
            if perf_report['cache_hit_rate'] < 5.0:
                result['issues'].append(f"缓存命中率过低: {perf_report['cache_hit_rate']:.1f}% < 5%")
            
            if total_time > 10.0:
                result['issues'].append(f"执行时间过长: {total_time:.1f}s > 10s")
                
        else:
            result['issues'].append("路径生成失败或结果为空")
            
        return result
        
    except Exception as e:
        return {
            'model_path': model_path,
            'complexity': complexity,
            'success': False,
            'error': str(e),
            'issues': [f"执行异常: {str(e)}"]
        }

def analyze_quality_metrics(spacing_data, target_spacing):
    """分析质量指标"""
    if not spacing_data:
        return {}
    
    spacings = [data['measured_3d_distance'] for data in spacing_data if data.get('measured_3d_distance', 0) > 0]
    
    if not spacings:
        return {}
    
    spacings = np.array(spacings)
    
    # 计算基本统计
    mean_spacing = np.mean(spacings)
    std_spacing = np.std(spacings)
    
    # 计算目标达成率
    tolerance_5 = target_spacing * 0.05
    within_tolerance = np.abs(spacings - target_spacing) <= tolerance_5
    target_achievement_rate = np.mean(within_tolerance) * 100
    
    # 计算RMS误差
    rms_error = np.sqrt(np.mean((spacings - target_spacing) ** 2))
    rms_error_percent = (rms_error / target_spacing) * 100
    
    return {
        'mean_spacing': mean_spacing,
        'std_spacing': std_spacing,
        'target_achievement_rate': target_achievement_rate,
        'rms_error': rms_error,
        'rms_error_percent': rms_error_percent,
        'min_spacing': np.min(spacings),
        'max_spacing': np.max(spacings),
        'spacing_count': len(spacings)
    }

def run_comprehensive_test():
    """运行全面测试"""
    print("🔬 nonplanar_slicer 全面模型稳定性测试")
    print("="*60)
    
    # 查找测试模型
    models = find_test_models()
    if not models:
        print("❌ 未找到测试模型文件")
        return
    
    print(f"📁 找到 {len(models)} 个测试模型:")
    for model in models:
        print(f"   - {model}")
    
    # 测试配置
    test_config = {
        'target_bead_width': 0.6,
        'path_row_spacing': 0.6 * 0.7,
        'path_max_segment_length': 0.6 * 2,
        'offset_distance_param': 0.6 * 0.1,
        'proximity_threshold_param': 0.1,
        'iter_min_delta_y_factor_param': 0.02,
        'iter_max_delta_y_factor_param': 1.5,
        'iter_tolerance_abs_param': 0.01,
        'iter_max_iterations_per_step_param': 5,
        'iter_num_samples_for_spacing_calc_param': 3
    }
    
    print(f"\n🔧 测试配置:")
    print(f"   - 目标线宽: {test_config['target_bead_width']} mm")
    print(f"   - 行距: {test_config['path_row_spacing']:.3f} mm")
    print(f"   - 最大迭代次数: {test_config['iter_max_iterations_per_step_param']}")
    print(f"   - 采样点数: {test_config['iter_num_samples_for_spacing_calc_param']}")
    
    # 测试所有模型
    results = []
    for model in models:
        result = test_model_performance(model, test_config)
        results.append(result)
    
    # 生成综合报告
    generate_comprehensive_report(results)

def generate_comprehensive_report(results):
    """生成综合测试报告"""
    print("\n" + "="*80)
    print("📊 综合测试报告")
    print("="*80)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"📈 测试概览:")
    print(f"   - 总测试数: {len(results)}")
    print(f"   - 成功: {len(successful_tests)} ✅")
    print(f"   - 失败: {len(failed_tests)} ❌")
    print(f"   - 成功率: {len(successful_tests)/len(results)*100:.1f}%")
    
    if successful_tests:
        print(f"\n⏱️  性能统计 (成功测试):")
        times = [r['total_time'] for r in successful_tests]
        cache_rates = [r['performance']['cache_hit_rate'] for r in successful_tests]
        
        print(f"   - 平均执行时间: {np.mean(times):.2f}s")
        print(f"   - 最快执行时间: {np.min(times):.2f}s")
        print(f"   - 最慢执行时间: {np.max(times):.2f}s")
        print(f"   - 平均缓存命中率: {np.mean(cache_rates):.1f}%")
        
        # 质量统计
        quality_results = [r for r in successful_tests if r.get('quality_metrics')]
        if quality_results:
            print(f"\n🎯 质量统计 (有质量数据的测试):")
            achievement_rates = [r['quality_metrics']['target_achievement_rate'] for r in quality_results]
            rms_errors = [r['quality_metrics']['rms_error_percent'] for r in quality_results]
            
            print(f"   - 平均目标达成率: {np.mean(achievement_rates):.1f}%")
            print(f"   - 最低目标达成率: {np.min(achievement_rates):.1f}%")
            print(f"   - 平均RMS误差: {np.mean(rms_errors):.1f}%")
            print(f"   - 最高RMS误差: {np.max(rms_errors):.1f}%")
    
    # 详细结果
    print(f"\n📋 详细测试结果:")
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {os.path.basename(result['model_path'])}")
        print(f"   复杂度: {result['complexity'].get('level', 'Unknown')}")
        
        if result['success']:
            print(f"   ✅ 成功 - 耗时: {result['total_time']:.2f}s")
            print(f"   缓存命中率: {result['performance']['cache_hit_rate']:.1f}%")
            
            if result.get('quality_metrics'):
                qm = result['quality_metrics']
                print(f"   目标达成率: {qm['target_achievement_rate']:.1f}%")
                print(f"   RMS误差: {qm['rms_error_percent']:.1f}%")
            
            if result.get('issues'):
                print(f"   ⚠️  问题: {'; '.join(result['issues'])}")
        else:
            print(f"   ❌ 失败")
            if 'error' in result:
                print(f"   错误: {result['error']}")
            if result.get('issues'):
                print(f"   问题: {'; '.join(result['issues'])}")

if __name__ == "__main__":
    run_comprehensive_test()
