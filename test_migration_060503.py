#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 nonplanar_slicer 060503.py 移植功能的验证脚本
验证以下移植的功能：
1. 全面间距分析报告功能
2. 可配置偏移距离参数设置
3. 路径条带修剪配置参数
4. 第一个切面和最后一个切面的特殊处理逻辑
"""

import sys
import os
import time
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入移植后的切片器
# 注意：文件名包含空格，需要使用importlib
import importlib.util
spec = importlib.util.spec_from_file_location("nonplanar_slicer_060503", "nonplanar_slicer 060503.py")
nonplanar_slicer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(nonplanar_slicer_module)
DirectProjectionSlicer = nonplanar_slicer_module.DirectProjectionSlicer

def test_migrated_features():
    """测试移植的功能"""
    print("="*80)
    print("测试 nonplanar_slicer 060503.py 移植功能")
    print("="*80)
    
    # 测试文件路径
    test_stl_file = "test_models/simple_cube.stl"
    
    # 检查测试文件是否存在
    if not os.path.exists(test_stl_file):
        print(f"警告: 测试文件 {test_stl_file} 不存在，将跳过实际测试")
        print("但可以验证类的初始化和方法存在性...")
        
        # 创建一个简单的测试STL文件
        try:
            import trimesh
            # 创建一个简单的立方体
            cube = trimesh.creation.box(extents=[10, 10, 5])
            os.makedirs("test_models", exist_ok=True)
            cube.export(test_stl_file)
            print(f"已创建测试文件: {test_stl_file}")
        except Exception as e:
            print(f"无法创建测试文件: {e}")
            return False
    
    try:
        # 1. 测试类初始化和新增的配置参数
        print("\n1. 测试类初始化和性能优化设置...")
        slicer = DirectProjectionSlicer(
            mesh_path=test_stl_file,
            target_surface_distance=0.4,
            slice_direction='x',
            inward_normals=True
        )
        print("✅ 类初始化成功")
        
        # 2. 测试新增的方法是否存在
        print("\n2. 测试移植的方法是否存在...")
        
        # 检查清除缓存方法
        if hasattr(slicer, 'clear_spacing_cache'):
            slicer.clear_spacing_cache()
            print("✅ clear_spacing_cache 方法存在并可调用")
        else:
            print("❌ clear_spacing_cache 方法不存在")
            
        # 检查性能优化方法
        if hasattr(slicer, '_optimize_performance_settings'):
            print("✅ _optimize_performance_settings 方法存在")
        else:
            print("❌ _optimize_performance_settings 方法不存在")
            
        # 检查切片覆盖验证方法
        if hasattr(slicer, 'validate_slice_coverage'):
            # 测试切片覆盖验证
            test_slice_positions = [1.0, 2.0, 3.0, 4.0, 5.0]
            test_mesh_bounds = np.array([[0, 0, 0], [6, 6, 3]])
            coverage_result = slicer.validate_slice_coverage(
                test_slice_positions, test_mesh_bounds, 0, 0.15)
            print("✅ validate_slice_coverage 方法存在并可调用")
            print(f"    测试结果: 切片数量={coverage_result['slice_count']}, "
                  f"覆盖充分={coverage_result['coverage_adequate']}")
        else:
            print("❌ validate_slice_coverage 方法不存在")
            
        # 检查间距分析方法
        if hasattr(slicer, '_calculate_actual_3d_spacing_between_strip_sets'):
            print("✅ _calculate_actual_3d_spacing_between_strip_sets 方法存在")
        else:
            print("❌ _calculate_actual_3d_spacing_between_strip_sets 方法不存在")
            
        if hasattr(slicer, 'log_comprehensive_spacing_analysis'):
            print("✅ log_comprehensive_spacing_analysis 方法存在")
        else:
            print("❌ log_comprehensive_spacing_analysis 方法不存在")
            
        if hasattr(slicer, 'save_spacing_analysis_to_file'):
            print("✅ save_spacing_analysis_to_file 方法存在")
        else:
            print("❌ save_spacing_analysis_to_file 方法不存在")
        
        # 3. 测试可配置参数的路径生成
        print("\n3. 测试可配置参数的路径生成...")
        
        # 测试参数配置
        target_bead_width = 0.4
        offset_distance_multiplier = 0.1
        path_strip_trim_multiplier = 0.1
        
        offset_distance_param = target_bead_width * offset_distance_multiplier
        path_strip_trim_length = target_bead_width * path_strip_trim_multiplier
        
        print(f"    目标珠宽: {target_bead_width}mm")
        print(f"    偏移距离倍数: {offset_distance_multiplier}")
        print(f"    计算的偏移距离: {offset_distance_param}mm")
        print(f"    路径条带修剪倍数: {path_strip_trim_multiplier}")
        print(f"    计算的修剪长度: {path_strip_trim_length}mm")
        
        # 4. 测试路径生成（简化测试）
        print("\n4. 测试路径生成功能...")
        
        try:
            paths_data = slicer.create_projected_fill_paths(
                row_spacing=0.4,
                offset_distance=offset_distance_param,
                max_segment_length=1.0,
                strategy='direct_offset',
                proximity_threshold=0.15,
                adaptive_density=True,
                path_strip_trim_multiplier=path_strip_trim_multiplier
            )
            print("✅ 路径生成成功")
            print(f"    生成的路径段数: {len(paths_data)}")
            
        except Exception as e:
            print(f"❌ 路径生成失败: {e}")
            
        # 5. 测试间距分析功能
        print("\n5. 测试间距分析功能...")
        
        # 创建模拟的间距数据
        mock_spacing_data = [
            {
                'ActualAvg3DSpacing_mm': 0.38,
                'NumSamples': 100,
                'Target3DSpacing_mm': 0.4,
                'Error_mm': -0.02
            },
            {
                'ActualAvg3DSpacing_mm': 0.42,
                'NumSamples': 95,
                'Target3DSpacing_mm': 0.4,
                'Error_mm': 0.02
            }
        ]
        
        try:
            slicer.log_comprehensive_spacing_analysis(
                mock_spacing_data, target_spacing=0.4, runtime_seconds=5.2)
            print("✅ 间距分析日志功能正常")
        except Exception as e:
            print(f"❌ 间距分析日志功能失败: {e}")
            
        try:
            slicer.save_spacing_analysis_to_file(
                mock_spacing_data, "test_spacing_analysis.csv")
            print("✅ 间距分析保存功能正常")
        except Exception as e:
            print(f"❌ 间距分析保存功能失败: {e}")
        
        print("\n" + "="*80)
        print("移植功能测试完成")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_migrated_features()
    if success:
        print("\n🎉 所有移植功能测试通过！")
    else:
        print("\n⚠️ 部分移植功能测试失败，请检查代码。")
